import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Send,
  Mic,
  Globe,
  Bot,
  Lightbulb,
  TrendingUp,
  AlertTriangle,
} from "lucide-react";
import { useSpeechRecognition } from "react-speech-kit";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { ReactTransliterate } from "react-transliterate";
import { cn } from "@/lib/utils";

const languageData = {
  "en-US": { name: "English", nativeName: "English", country: "en-US" },
  "hi-IN": { name: "Hindi", nativeName: "हिन्दी", country: "hi-IN" },
  "mr-IN": { name: "Marathi", nativeName: "मराठी", country: "mr-IN" },
};

const quickActions = [
  {
    title: "Quality Analysis",
    description: "Comprehensive quality metrics analysis",
    icon: <AlertTriangle className="h-5 w-5" />,
    action: "Show me detailed quality analysis for all production lines",
  },
  {
    title: "Production Forecast",
    description: "Advanced production predictions",
    icon: <TrendingUp className="h-5 w-5" />,
    action: "Generate detailed production forecast for next week",
  },
  {
    title: "Energy Optimization",
    description: "AI-powered energy recommendations",
    icon: <Lightbulb className="h-5 w-5" />,
    action: "Provide comprehensive energy optimization strategies",
  },
  {
    title: "Route Optimization",
    description: "Milk collection route analysis",
    icon: <Bot className="h-5 w-5" />,
    action: "Analyze and optimize milk collection routes",
  },
];

const sampleResponses = {
  "Show me detailed quality analysis for all production lines": {
    type: "analysis",
    content: "Comprehensive Quality Analysis: ...",
  },
  "Generate detailed production forecast for next week": {
    type: "forecast",
    content: "7-Day Production Forecast: ...",
  },
};

const IntegratedChat = () => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [input, setInput] = useState("");
  const [language, setLanguage] = useState("en-US");
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const bottomRef = useRef(null);

  const { listen, listening, stop, supported } = useSpeechRecognition({
    onResult: (result) => {
      setNewMessage(result);
    },
    onEnd: () => {
      setIsListening(false);
    },
    onError: (error) => {
      console.error("Speech recognition error:", error);
      setIsListening(false);
    },
  });

  useEffect(() => {
    const generateSessionId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    };
    const newSessionId = generateSessionId();
    setSessionId(newSessionId);
    setMessages([]);
  }, []);

  const handleSendMessage = async (messageText = null) => {
    const messageToSend = messageText || newMessage.trim();
    if (!messageToSend) return;

    const userMessage = {
      id: messages.length + 1,
      content: messageToSend,
      type: "user",
      time: new Date().toLocaleTimeString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setNewMessage("");
    setIsTyping(true);

    try {
      const res = await fetch("https://n8n.onpointsoft.com/webhook/gokul", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          query: messageToSend,
          language: language.split("-")[0],
          sessionId,
          timestamp: Date.now(),
        }),
      });

      if (!res.ok) throw new Error("API Error");

      const data = await res.json();
      const botMessage = {
        id: messages.length + 2,
        content: data.output,
        type: "ai",
        time: new Date().toLocaleTimeString(),
      };

      setMessages((prev) => [...prev, botMessage]);
    } catch (err) {
      console.error("Error:", err);
      setMessages((prev) => [
        ...prev,
        {
          id: messages.length + 2,
          content: "क्षमस्व, संदेश प्रक्रिया करण्यात अडचण आली.",
          type: "ai",
          time: new Date().toLocaleTimeString(),
        },
      ]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickAction = (actionMessage) => {
    handleSendMessage(actionMessage);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  useEffect(() => {
    // Disable Google Translate when speech recognition is active
    if (listening) {
      const googleTranslateElement = document.getElementById(
        "google_translate_element"
      );
      if (googleTranslateElement) {
        googleTranslateElement.style.display = "none";
      }
    } else {
      const googleTranslateElement = document.getElementById(
        "google_translate_element"
      );
      if (googleTranslateElement) {
        googleTranslateElement.style.display = "block";
      }
    }
  }, [listening]);

  const toggleMic = () => {
    // Properly handle Google Translate before starting speech recognition
    if (window.google && window.google.translate) {
      try {
        // Hide Google Translate widget instead of calling restore
        const googleTranslateElement = document.getElementById(
          "google_translate_element"
        );
        if (googleTranslateElement) {
          googleTranslateElement.style.display = "none";
        }
      } catch (error) {
        console.warn("Google Translate handling error:", error);
      }
    }

    if (listening) {
      stop();
      setIsListening(false);

      // Re-show Google Translate when mic is off
      const googleTranslateElement = document.getElementById(
        "google_translate_element"
      );
      if (googleTranslateElement) {
        googleTranslateElement.style.display = "block";
      }
    } else {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(() => {
          const languageCode = language || "en-US";
          listen({
            lang: languageCode,
            continuous: true,
            interimResults: true,
          });
          setIsListening(true);
        })
        .catch((error) => {
          console.error("Microphone permission denied:", error);
          setIsListening(false);

          // Re-show Google Translate on error
          const googleTranslateElement = document.getElementById(
            "google_translate_element"
          );
          if (googleTranslateElement) {
            googleTranslateElement.style.display = "block";
          }
        });
    }
  };

  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  return (
    <div className="  h-full bg-gradient-to-br from-blue-50 to-orange-50 p-6 notranslate ">
      <Card className="w-full h-full flex flex-col shadow-xl border-blue-100">
        {/* <CardHeader className="flex-shrink-0 bg-white border-b border-blue-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="border-2 border-blue-100">
                <AvatarImage src="https://chat.onpointsoft.com/onpoint-min.png" alt="Image" />
                <AvatarFallback className="bg-gradient-to-r from-blue-600 to-sky-600 text-white">OP</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium leading-none text-gray-900">onPoint Software Services</p>
                <p className="text-sm text-blue-600"><EMAIL></p>
              </div>
            </div>
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:bg-blue-50"
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              >
                <Globe className="h-5 w-5" />
              </Button>
              {showLanguageDropdown && (
                <div className="absolute right-0 mt-2 bg-white border border-blue-100 rounded-lg shadow-lg py-1 min-w-[120px] z-50">
                  {Object.entries(languageData).map(([code, lang]) => (
                    <div 
                      key={code} 
                      className="px-3 py-1.5 cursor-pointer hover:bg-blue-50 text-sm text-gray-700" 
                      onClick={() => { setLanguage(code); setShowLanguageDropdown(false); }}
                    >
                      {lang.nativeName}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardHeader> */}

        <CardContent className="flex-1 overflow-hidden bg-gradient-to-br from-blue-50 to-orange-50">
          <ScrollArea
            className="h-full p-4 space-y-4"
            onClick={() => setShowLanguageDropdown(false)}
          >
            {messages.length === 0 && (
              <div className="flex items-center justify-center h-full">
                <div className="grid grid-cols-2 gap-4 max-w-2xl w-full">
                  {quickActions.map((action, index) => (
                    <Card
                      key={index}
                      className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-blue-400 border-blue-100 hover:scale-105 transform active:scale-95"
                      onClick={() => handleQuickAction(action.action)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="bg-gradient-to-r from-blue-100 to-sky-100 p-2 rounded-lg flex-shrink-0 text-blue-600">
                            {action.icon}
                          </div>
                          <div className="min-w-0">
                            <h4 className="font-medium text-gray-900 text-sm">
                              {action.title}
                            </h4>
                            <p className="text-xs text-blue-600 mt-1">
                              {action.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.type === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`${
                    message.type === "user"
                      ? "max-w-xs lg:max-w-md"
                      : "max-w-sm lg:max-w-lg"
                  } p-3 rounded-lg ${
                    message.type === "user"
                      ? "bg-gradient-to-r from-blue-600 to-sky-600 text-white shadow-md"
                      : "bg-white shadow-md border border-blue-100"
                  }`}
                >
                  <div className="text-sm">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                        ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                        ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                        li: ({ children }) => <li className="mb-1">{children}</li>,
                        h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                        code: ({ children, className }) => {
                          const isInline = !className;
                          return isInline ? (
                            <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                          ) : (
                            <code className="block bg-gray-100 p-2 rounded text-xs font-mono overflow-x-auto">{children}</code>
                          );
                        },
                        pre: ({ children }) => <pre className="bg-gray-100 p-2 rounded mb-2 overflow-x-auto">{children}</pre>,
                        blockquote: ({ children }) => <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-2">{children}</blockquote>,
                        a: ({ children, href }) => <a href={href} className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">{children}</a>,
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                  {message.time && (
                    <p className="text-xs mt-2 opacity-70">{message.time}</p>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="max-w-sm lg:max-w-lg p-3 rounded-lg bg-white shadow-md border border-blue-100">
                  <div className="flex items-center space-x-2">
                    <div className="animate-pulse text-blue-600">
                      Processing your request...
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={bottomRef} />
          </ScrollArea>
        </CardContent>

        <div className="p-4 bg-white border-t border-blue-100 flex-shrink-0">
          <div className="flex space-x-2">
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:bg-blue-50"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowLanguageDropdown(!showLanguageDropdown);
                }}
              >
                <Globe className="h-5 w-5 mr-1" />
                <span className="text-sm font-medium">
                  {languageData[language]?.nativeName || "Language"}
                </span>
              </Button>

              {showLanguageDropdown && (
                <div className="absolute left-0 bottom-full mb-2 bg-white border border-blue-100 rounded-lg shadow-lg py-1 min-w-[120px] z-50">
                  {Object.entries(languageData).map(([code, lang]) => (
                    <div
                      key={code}
                      className="px-3 py-1.5 cursor-pointer hover:bg-blue-50 text-sm text-gray-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setLanguage(code);
                        setShowLanguageDropdown(false);
                      }}
                    >
                      {lang.nativeName}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {language === "en-US" ? (
              <Input
                placeholder={
                  listening || isListening
                    ? "🎤 Listening..."
                    : "Type your message here..."
                }
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                className={cn(
                  "flex-1 border-blue-200 focus:border-blue-400 focus:ring-blue-400",
                  (listening || isListening) &&
                    "border-red-400 ring-4 ring-red-100 bg-red-50"
                )}
              />
            ) : (
              <div className="flex-1">
                <ReactTransliterate
                  lang={language.split("-")[0]}
                  value={newMessage}
                  onChangeText={(text) => setNewMessage(text)}
                  onKeyPress={handleKeyPress}
                  placeholder={
                    listening || isListening
                      ? "🎤 Listening..."
                      : "येथे आपला संदेश टाइप करा..."
                  }
                  renderComponent={(props) => (
                    <input
                      {...props}
                      onKeyPress={handleKeyPress}
                      className={cn(
                        "w-full px-3 py-2 border border-blue-200 rounded-md focus:border-blue-400 focus:ring-blue-400 focus:outline-none",
                        (listening || isListening) &&
                          "border-red-400 ring-4 ring-red-100 bg-red-50"
                      )}
                      style={{
                        width: "100%",
                        minWidth: "100%",
                      }}
                    />
                  )}
                />
              </div>
            )}

            <Button
              type="button"
              onClick={toggleMic}
              disabled={!supported}
              className={cn(
                "flex-shrink-0 text-white",
                listening || isListening
                  ? "bg-red-500 hover:bg-red-600"
                  : "bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700",
                !supported && "opacity-50 cursor-not-allowed"
              )}
              title={
                !supported
                  ? "Speech recognition not supported"
                  : "Toggle microphone"
              }
            >
              <Mic className="h-4 w-4" />
            </Button>

            <Button
              onClick={handleSendMessage}
              size="icon"
              disabled={!newMessage.trim()}
              className={cn(
                "flex-shrink-0",
                newMessage.trim()
                  ? "bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700 text-white"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              )}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default IntegratedChat;
