import { useEffect } from 'react';

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

let scriptLoaded = false;

export const useGoogleTranslate = () => {
  useEffect(() => {
    if (scriptLoaded) return;

    window.googleTranslateElementInit = () => {
      new window.google.translate.TranslateElement(
        {
          pageLanguage: 'en',
          includedLanguages: 'en,hi,mr',
          layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
          autoDisplay: false,
          multilanguagePage: true,
        },
        'google_translate_element'
      );
    };

    const script = document.createElement('script');
    script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
    script.async = true;
    document.body.appendChild(script);

    const style = document.createElement('style');
    style.textContent = `
      .goog-te-banner-frame { display: none !important; }
      body { top: 0 !important; }

      .goog-te-gadget {
        font-family: 'Arial', sans-serif !important;
        font-size: 0 !important;
        margin: 0 !important;
        color: transparent !important;
        background: transparent !important;
        border: none !important;
      }
.VIpgJd-ZVi9od-xl07Ob-lTBxed {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    color: #333 !important;
    text-decoration: none !important;
    width: 100%;
  }
      #google_translate_element {
        display: flex !important;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: auto;
      }

      .goog-te-gadget-simple {
        background: #fff !important;
        border: 1px solid #ccc !important;
        border-radius: 8px;
        padding: 8px 12px !important;
        box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
        display: flex !important;
        align-items: center;
        gap: 8px;
        height: 48px;
        min-width: 160px;
      }

      .goog-te-gadget-simple span {
        font-size: 14px !important;
        color: #333 !important;
      }

      .goog-te-gadget-icon {
        width: 20px !important;
        height: 20px !important;
        background-image: url("https://translate.googleapis.com/translate_static/img/te_ctrl3.gif");
        background-position: -65px 0;
        background-size: auto;
        margin-right: 6px;
      }

      .skiptranslate iframe,
      .goog-te-balloon-frame {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
      }

      body.VIpgJd-ZVi9od-ORHb {
        top: 0px !important;
        margin-top: 0px !important;
      }
    `;
    document.head.appendChild(style);

    scriptLoaded = true;
  }, []);
};
