import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Factory,
  BarChart3,
  Package,
  Truck,
  Zap,
  Bell,
  Bot,
  ChevronLeft,
  ChevronRight,
  X,
  FileText,
  Camera,
  Settings,
  Brain,
} from "lucide-react";
import MainDashboard from "@/components/MainDashboard";
import QualityDepartment from "@/components/QualityDepartment";
import SalesDepartment from "@/components/SalesDepartment";
import ProductionDepartment from "@/components/ProductionDepartment";
import ProcessingDepartment from "@/components/ProcessingDepartment";
import DispatchDepartment from "@/components/DispatchDepartment";
import EnergyDepartment from "@/components/EnergyDepartment";
import ReportsDepartment from "@/components/ReportsDepartment";
import PhotoGallery from "@/components/PhotoGallery";
import AIInsights from "@/components/AIInsights";
import AIAssistant from "@/components/AIAssistant";
import NotificationPanel from "@/components/NotificationPanel";
import AllAlerts from "@/components/AllAlerts";
import FinanceDepartment from "@/components/FinanceDepartment";
import GokulGPT from "@/components/GokulGPT";
import GoogleTranslate from "@/components/GoggleTranslate";

const Index = () => {
  const [activeMenu, setActiveMenu] = useState("dashboard");
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [showNotifications, setShowNotifications] = useState(false);

  const menuItems = [
    {
      id: "dashboard",
      title: "Main Dashboard",
      icon: <BarChart3 className="h-5 w-5" />,
      hasSubmenu: false,
    },
    {
      id: "ai-insights",
      title: "AI Insights",
      icon: <Brain className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "quality",
      title: "Quality Control",
      icon: <Package className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "sales",
      title: "Sales & Distribution",
      icon: <Truck className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "finance",
      title: "Finance",
      icon: <FileText className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "production",
      title: "Production",
      icon: <Factory className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "processing",
      title: "Processing",
      icon: <Settings className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "dispatch",
      title: "Dispatch & Cold Chain",
      icon: <Truck className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "energy",
      title: "Energy Management",
      icon: <Zap className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "reports",
      title: "Reports & Analytics",
      icon: <FileText className="h-5 w-5" />,
      hasSubmenu: true,
    },
    {
      id: "photos",
      title: "Photo Gallery",
      icon: <Camera className="h-5 w-5" />,
      hasSubmenu: false,
    },
    {
      id: "all-alerts",
      title: "All Alerts",
      icon: <Bell className="h-5 w-5" />,
      hasSubmenu: false,
    },
    {
      id: "gokulgpt",
      title: "GokulGPT",
      icon: <Bot className="h-5 w-5" />,
      description: "AI Assistant",
    },
  ];

  const renderContent = () => {
    switch (activeMenu) {
      case "dashboard":
        return <MainDashboard onDepartmentClick={handleMenuClick} />;
      case "ai-insights":
        return <AIInsights />;
      case "quality":
        return <QualityDepartment />;
      case "sales":
        return <SalesDepartment />;
      case "finance":
        return <FinanceDepartment />;
      case "production":
        return <ProductionDepartment />;
      case "processing":
        return <ProcessingDepartment />;
      case "dispatch":
        return <DispatchDepartment />;
      case "energy":
        return <EnergyDepartment />;
      case "reports":
        return <ReportsDepartment />;
      case "photos":
        return <PhotoGallery />;
      case "all-alerts":
        return <AllAlerts />;
      case "gokulgpt":
        return <GokulGPT />;
      default:
        return <MainDashboard onDepartmentClick={handleMenuClick} />;
    }
  };

  const handleMenuClick = (menuId: string) => {
    setActiveMenu(menuId);
    console.log(`Navigating to ${menuId} department`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-orange-50 flex font-inter">
      {/* Fixed Sidebar */}
      <div
        className={`${
          sidebarOpen ? "w-72" : "w-28"
        } bg-white shadow-xl transition-all duration-300 flex flex-col border-r border-blue-100 fixed h-screen z-30`}
      >
        {/* Header with Large Logo */}
        <div className="p-6 border-b border-blue-100">
          <div className="flex items-center justify-center relative">
            <div
              className={`flex items-center mr-8 ${
                !sidebarOpen && "justify-center"
              }`}
            >
              <div className="flex-shrink-0">
                <img
                  src="/lovable-uploads/9aaf195e-005f-478b-be99-d410d7942db5.png"
                  alt="Gokul Dairy Logo"
                  className={`object-contain transition-all duration-300 ${
                    sidebarOpen ? "h-30 w-36" : "h-12 w-12"
                  }`}
                />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="hover:bg-blue-50 p-2 justify-end absolute top-4 -right-4"
            >
              {sidebarOpen ? (
                <ChevronLeft className="h-4 w-4 text-blue-600" />
              ) : (
                <ChevronRight className="h-4 w-4 text-blue-600" />
              )}
            </Button>
          </div>
        </div>

        {/* Navigation Menu - Scrollable */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {menuItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleMenuClick(item.id)}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 group ${
                activeMenu === item.id
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105"
                  : "text-gray-700 hover:bg-blue-50 hover:text-blue-700 hover:shadow-md"
              }`}
            >
              <div
                className={`${
                  activeMenu === item.id ? "text-white" : "text-blue-500"
                } transition-colors`}
              >
                {item.icon}
              </div>
              {sidebarOpen && (
                <div className="flex-1">
                  <span className="text-sm font-semibold">{item.title}</span>
                  {/* {item.hasSubmenu && (
                    <div className="text-xs opacity-75 mt-1">
                      Multiple views available
                    </div>
                  )} */}
                </div>
              )}
            </button>
          ))}
        </nav>

        {/* Footer Info */}
        {sidebarOpen && (
          <div className="p-4 border-t border-blue-100 bg-gradient-to-r from-blue-50 to-orange-50">
            <div className="text-xs text-gray-600 space-y-1">
              <div className="font-medium text-blue-700">
                Daily Capacity: 17K Litres
              </div>
              <div className="text-green-600">ISO 9001:2015 Certified</div>
              <div className="text-gray-500">Kolhapur, Maharashtra</div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content with Left Margin */}
      <div
        className={`flex-1 flex flex-col ${
          sidebarOpen ? "ml-72" : "ml-28"
        } transition-all duration-300`}
      >
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-blue-100 px-6 py-4">
          <div className="flex items-center justify-between">
            {activeMenu === "gokulgpt" ? (
              <div className="flex items-center gap-4 bg-white rounded-xl p-4">
                <div className="bg-gradient-to-r from-blue-600 to-sky-600 p-3 rounded-full">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {menuItems.find((item) => item.id === activeMenu)?.title}
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Advanced AI Assistant for Dairy Operations
                  </p>
                </div>
              </div>
            ) : (
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {menuItems.find((item) => item.id === activeMenu)?.title}
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Real-time operational monitoring & AI-powered insights
                </p>
              </div>
            )}

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <div>
                <GoogleTranslate />
              </div>
              {/* AI Assistant Button */}
              <Button
                onClick={() => setActiveMenu("gokulgpt")}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg"
              >
                <Bot className="h-4 w-4 mr-2" />
                GokulGPT
              </Button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 p-6 overflow-auto bg-gradient-to-br from-blue-50/30 to-orange-50/30">
          {renderContent()}
        </main>
      </div>

      {/* Notifications Panel */}
      {showNotifications && (
        <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 border-l border-blue-100">
          <NotificationPanel onClose={() => setShowNotifications(false)} />
        </div>
      )}
    </div>
  );
};

export default Index;
